# ELECTION Situation Room — Product Requirements Document (PRD)

**Version:** 1.0
**Owner:** Product + Engineering
**Date:** 2025-08-16
**Platforms:** Web (HTML + Bootstrap + JS), Mobile (React Native)
**Backend:** <PERSON><PERSON> (REST API)
**Database:** MySQL

---

## 1) Executive Summary

The **ELECTION Situation Room** is a real‑time monitoring platform for elections. Field **Agents (Users)** report polling results and incidents from their assigned **Ward** and **Local Government Area (LGA)** via web or mobile. **Admins** manage entities (Users, Wards, LGAs, Parties), validate data, analyze votes, resolve issues, and export/print reports.

This PRD defines the scope for **MVP v1** with a scalable data model and API that can later support additional elections, polling units, geolocation, and offline data capture.

---

## 2) Goals & Non‑Goals

### 2.1 Goals

* Fast, reliable capture of ward‑level results by assigned Agents.
* Simple incident/issue reporting with media proof (images/videos).
* Clean Admin analytics (totals, latest submissions, filters by Ward/LGA/Party).
* Robust data export (Excel/PDF) and printable result views.
* Strict role‑based access and an **edit cap (3 edits)** per submission by Agent.

### 2.2 Non‑Goals (v1)

* Full polling‑unit granularity (optional in future).
* Advanced fraud detection, AI OCR, or real‑time media verification.
* Public portal/visualizations for general audience.
* Multi‑election scheduling UI (we design for it, but ship single active election by default).

---

## 3) Users & Roles

* **Agent (User):** Assigned to exactly one Ward (and its LGA). Can submit vote results for their Ward, report issues, contact Admin, and log out. Can view profile (read‑only). Can edit their own submitted result up to **3 times**.
* **Admin:** Full access. Manages users, wards, LGAs, parties; views/filters/analyzes votes and issues; can export/print; may submit votes on behalf of Agents when needed.

> Optional future roles: Super Admin (system owner), Auditor/Read‑only Analyst.

---

## 4) High‑Level Scope (MVP v1)

1. **Authentication**: Login by **Phone Number OR Ward ID** + Secret (PIN/Password). Admin creates/assigns credentials. (OTP is a future enhancement.)
2. **Agent Dashboard** (Web & Mobile): Welcome panel, Ward + LGA name, **card slider** (voting info banners), and **4 icon cards**: Submit Result, Report Issue, Contact Admin, Logout. Profile page is read‑only.
3. **Result Submission**: Party vote inputs (dynamic list from Admin‑managed Parties), optional “Others (custom)” entry, upload **result sheet image**, submit; edit allowed **max 3 times**.
4. **Issue Reporting**: Select Admin‑defined issue type or write message; attach image/video proof; submit.
5. **Contact Admin**: Phone call link, WhatsApp deep link, and in‑app message form.
6. **Admin Dashboard**: KPIs (Agents, Votes Cast, Wards, LGAs), latest vote submissions, recent users.
7. **Entity Management**: Users (Agents), Wards, LGAs, Parties — full CRUD.
8. **Vote Management**: Analysis by Party, filters (Ward/LGA), add votes on behalf of Agent, export to **PDF/Excel**, detailed view with media, printable.
9. **Issue Management**: View/filter issues, open details with proof media, mark status, respond.
10. **Auditing & Limits**: Track submission edit counts and change logs.

---

## 5) User Stories & Acceptance Criteria

### 5.1 Authentication

**As an Agent**, I can log in using my **Phone Number OR Ward ID** plus a secret (PIN/Password), so I can access my dashboard.

* **AC1:** Accept either phone or ward\_id as the login identifier.
* **AC2:** Incorrect secret returns clear error without revealing which field was wrong.
* **AC3:** Account lock after 5 failed attempts (15 min), Admin unlock in User Management.
* **AC4:** Session persists for 24h on mobile app unless logout.

### 5.2 Agent Dashboard

**As an Agent**, I see my **Ward** and **LGA** in a welcome message and a **card slider** with voting info.

* **AC1:** Welcome text includes `AgentName — WardName, LGAName`.
* **AC2:** Card slider supports 3–5 images with captions (configurable by Admin Settings).
* **AC3:** 4 icon cards are visible: Submit Result, Report Issue, Contact Admin, Logout.

### 5.3 Submit Result (with 3‑Edit Cap)

**As an Agent**, I can submit vote counts per Party for my Ward and upload the result sheet image.

* **AC1:** Party list loads from Admin‑managed parties; fields accept only non‑negative integers.
* **AC2:** Optional “Others” entry allows Party Name + Votes when Party not pre‑listed.
* **AC3:** At least one Party must have a >0 vote to submit (or allow zero totals if needed by Admin policy; configurable).
* **AC4:** One or more images can be uploaded (JPG/PNG, max size configurable). Video optional (MP4, size cap).
* **AC5:** After submit, show success page and summary.
* **AC6:** Agent can **edit** their own latest submission up to **3 times**. UI shows remaining edits (3‑N). Beyond 3 edits, block and show guidance to contact Admin.
* **AC7:** Audit log records every edit (who, when, before/after deltas).

### 5.4 Report Issue

**As an Agent**, I can report an issue with a selected type or a typed message, and attach proof.

* **AC1:** Issue type dropdown populated from Admin settings; free‑text message always allowed.
* **AC2:** Support media attachments (image/video) with size/type validation.
* **AC3:** On success, show confirmation and issue reference ID.

### 5.5 Contact Admin

**As an Agent**, I can quickly reach Admin.

* **AC1:** **Phone** link (`tel:`), **WhatsApp** deep link (`https://wa.me/<number>?text=<encoded>`), **In‑App Message** (simple thread).
* **AC2:** Messages appear in Admin panel with Agent, Ward, LGA context.

### 5.6 Logout & Profile

* **AC1:** Logout clears session and returns to login.
* **AC2:** Profile page is read‑only (Agent cannot edit name, ward, or LGA). Admin can update via User Management.

### 5.7 Admin Dashboard

* **AC1:** KPIs: Total Agents, Total Votes Cast, Total Wards, Total LGAs.
* **AC2:** Widgets: Latest vote submissions (table, 10 items), Recent users (5 items).
* **AC3:** Charts: Votes by Party (bar/pie), Submissions over time (line).

### 5.8 User Management (Admin)

* **AC1:** CRUD Agents; assign Ward & LGA; set/reset login secret.
* **AC2:** Bulk import (CSV) optional (v1.1) with sample template.
* **AC3:** Deactivate/activate users.

### 5.9 Ward & LGA Management (Admin)

* **AC1:** CRUD Wards; each Ward belongs to one LGA.
* **AC2:** CRUD LGAs.

### 5.10 Party Management (Admin)

* **AC1:** CRUD Parties with **name + logo**.
* **AC2:** Pre‑seed sample: **SDP, PDP, APC, Others**.

### 5.11 Vote Submission Management (Admin)

* **AC1:** Table of all submissions with filters: Ward, LGA, Party, Date.
* **AC2:** Click row to open detailed view (Agent, Ward/LGA, media, per‑party votes, timestamps, edit history).
* **AC3:** Add votes on behalf of an Agent (select Agent/Ward, fill votes, attach sheet). Marks `created_by=admin`.
* **AC4:** Exports: **Excel** (xlsx via Laravel Excel) and **PDF** (DomPDF) — respect current filters.
* **AC5:** Printable detail page.

### 5.12 Issue Management (Admin)

* **AC1:** Table of issues (status: Open/In‑Progress/Resolved), filters by Ward/LGA/Type/Date.
* **AC2:** Detail view with media, message, Agent info; internal notes & status updates.

### 5.13 Edit Limits & Auditing

* **AC1:** A submission has `edit_count` (starting at 0). PUT/PATCH increments up to max 3 for Agent‑initiated edits.
* **AC2:** Admin can override edits without affecting Agent’s limit; audit log records `role` on change.

---

## 6) Information Architecture & Navigation

### 6.1 Agent (Web & Mobile)

* **Login** → **Dashboard** (Welcome + Slider + 4 Cards)

  * Submit Result
  * Report Issue
  * Contact Admin
  * Logout
* **Profile (read‑only)**

### 6.2 Admin (Web)

* **Dashboard** (KPIs + latest submissions + recent users + charts)
* **Users** (CRUD)
* **Wards** (CRUD)
* **LGAs** (CRUD)
* **Parties** (CRUD)
* **Votes** (Table + Detail + Add + Exports + Print)
* **Issues** (Table + Detail + Status)
* **Settings** (WhatsApp number, phone, slider banners, media limits, edit cap value, feature flags)

---

## 7) Data Model (MySQL)

> Designed for single active election in v1; model is **future‑proof** for multi‑election by introducing an `elections` table later.

### 7.1 Tables

**users**

* id (PK, bigint)
* name (varchar 120)
* phone (varchar 20, unique nullable)
* ward\_id (FK wards.id)
* lga\_id (FK lgas.id)
* role (enum: `agent`, `admin`)
* login\_identifier (generated: phone or ward\_id string; indexed)
* secret\_hash (password/PIN hash)
* status (enum: `active`, `locked`, `inactive`)
* last\_login\_at (datetime nullable)
* created\_at/updated\_at

**lgas**

* id (PK)
* name (varchar 120 unique)
* created\_at/updated\_at

**wards**

* id (PK)
* lga\_id (FK lgas.id, index)
* name (varchar 120)
* UNIQUE(lga\_id, name)
* created\_at/updated\_at

**parties**

* id (PK)
* name (varchar 50 unique)
* logo\_path (varchar 255 nullable)
* created\_at/updated\_at

**submissions** (one per Agent per Ward per reporting cycle)

* id (PK)
* user\_id (FK users.id)
* ward\_id (FK wards.id)
* lga\_id (FK lgas.id)
* total\_votes (int, cached sum)
* edit\_count (tinyint default 0)
* created\_by\_role (enum: `agent`, `admin`)
* status (enum: `submitted`, `edited`, `locked`) — v1 minimal
* submitted\_at (datetime)
* updated\_at (datetime)
* INDEX (ward\_id, lga\_id, submitted\_at)

**submission\_items** (per‑party lines)

* id (PK)
* submission\_id (FK submissions.id, index)
* party\_id (FK parties.id nullable if Others)
* other\_party\_name (varchar 60 nullable)
* votes (int >= 0)
* UNIQUE(submission\_id, party\_id, other\_party\_name)

**submission\_files** (images/videos)

* id (PK)
* submission\_id (FK submissions.id, index)
* media\_type (enum: `image`, `video`)
* path (varchar 255)
* created\_at

**submission\_edits** (audit)

* id (PK)
* submission\_id (FK)
* edited\_by\_user\_id (FK users.id)
* editor\_role (enum: `agent`, `admin`)
* changes\_json (json)  // before→after deltas
* created\_at (datetime)

**issues**

* id (PK)
* user\_id (FK users.id)
* ward\_id (FK wards.id)
* lga\_id (FK lgas.id)
* issue\_type (varchar 80) // from Settings presets or free‑text
* message (text)
* status (enum: `open`, `in_progress`, `resolved`) default `open`
* created\_at/updated\_at

**issue\_files**

* id (PK)
* issue\_id (FK issues.id, index)
* media\_type (enum: `image`, `video`)
* path (varchar 255)
* created\_at

**messages** (Agent ↔ Admin simple thread)

* id (PK)
* from\_user\_id (FK users.id)
* to\_user\_id (FK users.id) // Admin user id
* body (text)
* read\_at (datetime nullable)
* created\_at

**settings**

* id (PK)
* whatsapp\_number (varchar 20)
* admin\_phone (varchar 20)
* slider\_items\_json (json) // images/captions/links
* max\_edit\_count (tinyint default 3)
* max\_image\_mb (int default 5)
* max\_video\_mb (int default 25)
* created\_at/updated\_at

**audit\_logs** (system‑wide)

* id (PK)
* actor\_user\_id (FK users.id)
* actor\_role (enum)
* action (varchar 60)
* entity (varchar 60)
* entity\_id (bigint)
* meta (json)
* created\_at

> **Indexes:** Ensure indexes on FK columns and common filters (ward\_id, lga\_id, party\_id, submitted\_at, status). Consider composite indexes for analytics queries.

---

## 8) API Specification (Laravel REST)

Base URL: `/api/v1`

### 8.1 Auth

* **POST** `/auth/login`

  * **Body:** `{ "identifier": "0703…" | "WARD-001", "secret": "1234" }`
  * **200:** `{ token, user: {id,name,role,ward,{id,name},lga{...}} }`
  * **401:** `{ error: "Invalid credentials" }`

* **GET** `/auth/me` (Bearer)

* **POST** `/auth/logout` (Bearer)

### 8.2 Agent — Dashboard & Profile

* **GET** `/agent/summary`

  * Returns: welcome text data, slider items, edit limits, quick counts.
* **GET** `/profile` (read‑only)

### 8.3 Submissions

* **POST** `/submissions`

  * **Body:**

    ```json
    {
      "party_votes": [
        {"party_id": 1, "votes": 120},
        {"party_id": 2, "votes": 80},
        {"other_party_name": "XYZ", "votes": 5}
      ],
      "files": ["<fileRef1>", "<fileRef2>"]
    }
    ```
  * Files uploaded via `/uploads` pre‑signed endpoint or multipart form.
  * **201:** `{ submission_id, edit_count: 0 }`

* **PUT/PATCH** `/submissions/{id}`

  * **Rules:** Reject if `edit_count >= settings.max_edit_count` and editor is Agent.
  * **200:** `{ submission_id, edit_count }`

* **GET** `/submissions?ward_id=&lga_id=&from=&to=` (role‑aware: Agent sees own; Admin sees all)

* **GET** `/submissions/{id}` (detail with items, files, audit)

### 8.4 Issues

* **POST** `/issues`

  * **Body:** `{ "issue_type": "Intimidation", "message": "…", "files": ["<fileRef>"] }`
  * **201:** `{ issue_id, status: "open" }`
* **GET** `/issues` (Agent: own; Admin: all + filters)
* **GET** `/issues/{id}`
* **PATCH** `/issues/{id}` (Admin: update status/notes)

### 8.5 Contact (In‑App Messages)

* **POST** `/messages`
* **GET** `/messages/thread/{user_id}`
* **PATCH** `/messages/{id}/read`

### 8.6 Admin — Entities

* **CRUD** `/admin/users`
* **CRUD** `/admin/wards`
* **CRUD** `/admin/lgas`
* **CRUD** `/admin/parties`
* **GET** `/admin/metrics` — dashboard KPIs
* **GET** `/admin/analytics/votes?by=party&ward_id=&lga_id=&from=&to=`
* **POST** `/admin/submissions` — create on behalf of Agent
* **GET** `/admin/exports/votes.xlsx` (filters accepted)
* **GET** `/admin/exports/votes.pdf` (filters accepted)
* **GET** `/admin/issues` (filters)
* **GET** `/admin/issues/{id}`

### 8.7 Validation Rules (key)

* `votes` integers ≥ 0; totals auto‑sum; party list deduplicated.
* Images: JPG/PNG, ≤ `settings.max_image_mb` MB each; Videos: MP4, ≤ `settings.max_video_mb` MB.
* For edits: verify editor role & `edit_count` before applying.

---

## 9) UI/UX Requirements

### 9.1 Agent — Web (Bootstrap) & Mobile (React Native)

* **Dashboard**

  * Welcome banner with name + Ward + LGA.
  * **Card slider** (auto‑rotate, swipe on mobile, pause on hover).
  * **4 Icon Cards** with labels:

    1. **Submit Result**
    2. **Report Issue**
    3. **Contact Admin**
    4. **Logout**
* **Submit Result**

  * Dynamic list of Parties with inline logos.
  * “Others” row: Party Name + Votes (add/remove row UI).
  * **Upload**: result sheet (min 1 image required by default; configurable). Preview thumbnails.
  * **CTA**: Submit → Success screen with summary & edit remaining counter.
  * **Edit flow**: Same form pre‑filled; show "Edits left: X/3".
* **Report Issue**

  * Type dropdown + text area, media attachments, submit → Success.
* **Contact Admin**

  * Buttons: **Call**, **WhatsApp**, **In‑App Message**. Show configured numbers.
* **Profile (read‑only)**

  * Name, Phone, Ward, LGA. Note: “Contact Admin to change details”.

### 9.2 Admin — Web (Bootstrap)

* **Dashboard**

  * KPI cards + latest submissions + recent users + charts (Chart.js or ApexCharts).
* **Users/Wards/LGAs/Parties**

  * Table views with search/sort/pagination; modal forms for create/edit; delete with confirm.
* **Votes**

  * Table with filters (Ward/LGA/Party/Date). Row → Detail drawer/page.
  * Detail shows per‑party breakdown, files (lightbox gallery/video player), audit trail, print button.
  * Actions: Add on behalf, Export PDF/Excel.
* **Issues**

  * Table with status filters; detail with media and status updates; assign internal notes.
* **Settings**

  * WhatsApp/phone, slider banners, media limits, edit cap.

---

## 10) Non‑Functional Requirements

* **Security**: HTTPS, JWT auth, hashed secrets (bcrypt/argon2), role‑based middleware, file type/size validation, rate limiting on login & submissions.
* **Performance**: P95 API < 500ms for typical queries; image upload streaming; pagination on all lists.
* **Reliability**: Daily DB backups; object storage (S3/Spaces/Laravel storage) for media.
* **Scalability**: Separate stateless API; CDN for media; queue workers for exports.
* **Observability**: Request logs, error tracking (Sentry), audit logs for all CRUD.
* **Accessibility**: WCAG AA for web; large tap targets on mobile; clear error states.
* **Localization**: English (v1); framework to add Hausa/Yoruba/Igbo later.
* **Compliance**: Handle PII (phone) minimally; restrict access by role; data retention policy (e.g., 12–24 months).

---

## 11) Export & Print

* **Excel (xlsx)**: one row per submission with columns: SubmissionID, Date, Agent, Ward, LGA, \[Party columns dynamic], Total, File links, CreatedByRole.
* **PDF**: filter‑aware report and single‑submission printable page (includes images, metadata, signatures/space).

---

## 12) Edit‑Limit Logic (Detailed)

* Configurable global cap via `settings.max_edit_count` (default 3).
* On `PUT/PATCH /submissions/{id}` by Agent:

  * If `edit_count >= cap` → 403 `{ code: "EDIT_LIMIT_REACHED" }`.
  * Else: save changes, increment `edit_count`, append `submission_edits` row.
* Admin edits do **not** count towards Agent’s limit; they are still audited.

---

## 13) Permissions Matrix

| Action                          | Agent        | Admin         |
| ------------------------------- | ------------ | ------------- |
| Login                           | ✓            | ✓             |
| View own profile                | ✓            | ✓             |
| Edit own profile                | ✗            | ✓ (via Users) |
| Submit result (own Ward)        | ✓            | ✓ (on behalf) |
| Edit own submission (≤3)        | ✓            | ✓ (no limit)  |
| View all submissions            | ✗ (own only) | ✓             |
| Export votes                    | ✗            | ✓             |
| Report issue                    | ✓            | ✓ (log/admin) |
| Manage users/wards/LGAs/parties | ✗            | ✓             |

---

## 14) Tech Stack & Key Libraries

* **Web (Admin & Agent):** HTML, Bootstrap 5, Vanilla JS, Chart.js/ApexCharts.
* **Mobile:** React Native (Expo optional), Axios for API, React Navigation.
* **Backend:** Laravel 11+, Sanctum/JWT for auth, Eloquent ORM, Laravel Excel, DomPDF.
* **Realtime (optional v1.1):** Laravel Reverb or Pusher for live updates.
* **Storage:** Laravel Filesystem (Local/S3); image optimization (Intervention/Image) optional.

---

## 15) Migrations (Laravel Outline)

* `create_users_table`
* `create_lgas_table`
* `create_wards_table`
* `create_parties_table`
* `create_submissions_table`
* `create_submission_items_table`
* `create_submission_files_table`
* `create_submission_edits_table`
* `create_issues_table`
* `create_issue_files_table`
* `create_messages_table`
* `create_settings_table`
* `create_audit_logs_table`

> Seeders: Parties (SDP, PDP, APC, Others), Admin user, example Ward/LGA, Settings.

---

## 16) Data Validation & Constraints

* Votes must be integers ≥ 0; reject negatives and non‑numbers.
* At least one media file on submission (configurable minimum).
* Enforce Agent‑Ward scoping: Agents can only submit for **their assigned Ward**.
* Prevent duplicate active submission for same Agent/Ward per day (configurable); allow Admin override.

---

## 17) Reporting & Analytics

* **Charts:** Votes by Party (pie/bar), Submissions over Time (line), Issues by Type (bar).
* **Tables:** Filterable and exportable. All exports mirror on‑screen filters.

---

## 18) Error Handling & UX

* Clear inline validation messages.
* Upload failures show retry with file‑by‑file progress.
* Helpful empty states with CTAs.
* 403/401 redirect to login with message.

---

## 19) Deployment & DevOps

* Environments: **Dev**, **Staging**, **Production**.
* CI/CD: run tests, migrations, seed minimal data.
* SSL everywhere; environment secrets; rotate keys.
* Backups: nightly DB dump; weekly media snapshot.

---

## 20) KPIs & Success Metrics

* Average submission time (< 2 minutes from open → submit).
* Data completeness (% submissions with media attached ≥ 95%).
* Issue response time (median < 30 minutes during active periods).
* System uptime ≥ 99.5% during election days.

---

## 21) Risks & Mitigations

* **Network instability:** Add retry logic, resumable uploads (v1.1), light payloads.
* **User errors:** Strong validation, confirmation screens, visible edit counter.
* **Data overload:** Pagination + server‑side filters; async exports.
* **Security:** Rate limiting, brute‑force lockouts, least‑privilege RBAC, S3 presigned URLs.

---

## 22) Testing Plan

* **Unit Tests:** Models, validation, edit‑limit logic, role guards.
* **API Tests:** Auth, submissions CRUD, issues CRUD, filters, exports.
* **E2E (Cypress/Detox):** Agent flows (login → submit → edit), Admin flows (filters → export → print).
* **UAT Scripts:** Checklist for election day scenarios.

---

## 23) Roadmap (Post‑MVP)

* **Offline mode** with queued sync (mobile).
* **Geotagging** of submissions & issues.
* **Polling Unit** entity beneath Ward; map visualizations.
* **Multi‑election** scheduler + archived results.
* **Notifications** (FCM, email, SMS) on new submissions/issues.
* **OCR** to parse numbers from result sheets (assistive verification).

---

## 24) Glossary

* **LGA:** Local Government Area.
* **Ward:** Electoral subdivision within an LGA.
* **Party:** Political party (e.g., SDP, PDP, APC).
* **Submission:** A set of vote counts for a Ward with proof media.
* **Issue:** A reported incident with optional media.

---

## 25) Acceptance — MVP Definition of Done

* Agent can login, see welcome with Ward/LGA, view slider.
* Agent can submit votes with media; can edit up to 3 times; audit tracked.
* Agent can report issues with media.
* Agent can contact Admin via call/WhatsApp/in‑app.
* Admin dashboard shows KPIs, latest submissions, recent users.
* Admin can CRUD Users/Wards/LGAs/Parties.
* Admin can view, filter, add, export, and print vote submissions.
* Admin can view and manage issues.
* All APIs secured; validations enforced; basic test coverage ≥ 70%.

---

### Appendix A — Example Export Columns (Votes.xlsx)

* SubmissionID, SubmittedAt, AgentName, AgentPhone, Ward, LGA, CreatedByRole, \[Party\:SDP], \[Party\:PDP], \[Party\:APC], \[Party\:Others], TotalVotes, ImageURLs

### Appendix B — Example Print Layout (Submission.pdf)

* Header: Logo + Title + Date/Time
* Agent: Name, Phone, Ward, LGA
* Votes Table: Each Party + Votes + Total
* Media: Result sheet thumbnails (append separate page for full‑size)
* Footer: Signature lines (Agent, Admin), QR with SubmissionID

### Appendix C — Sample Error Codes

* `EDIT_LIMIT_REACHED`
* `INVALID_MEDIA_TYPE`
* `VALIDATION_ERROR`
* `UNAUTHORIZED`
* `FORBIDDEN`

---

**End of PRD v1.0**

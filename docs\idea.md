Frontend (Web): HTML + Bootstrap + JavaScript (for admin and web agent interface)

Mobile App (Agent): React Native (for Android/iOS)

Backend: <PERSON><PERSON> (REST API to power both web and mobile apps)

Database: MySQL

Export & Reporting: Laravel Excel (for Excel export), DomPDF (for PDF export)

🔹 User Roles

User (Agent)

Limited access, only to their ward.

Can submit results, report issues, contact admin, and log out.

Limited editing of results (max 3 times).

Admin

Full system access.

Manages users, wards, LGAs, parties, votes, and issues.

Can analyze results, filter, export, and print.

🔹 User (Agent) Dashboard Features

✅ Login Page – Login with phone number or ward ID.

✅ Welcome Banner – Shows agent name, ward, and local government.

✅ Card Slider (Voting Info) – Image-based slider showing election voting updates.

✅ 4 Dashboard Icons:

Submit Result

Inputs for SDP, PDP, APC, and OTHERS (dynamic, based on parties created by admin).

Upload result sheet image.

Submit button → Success Page.

Edit result (allowed only 3 times).

Report Issue

Select issue type (loaded from admin).

Or send custom message.

Upload photo/video proof.

Submit → Success page.

Contact Admin

Options: Phone Call, WhatsApp, In-App Messaging.

Logout

✅ Profile Page

View-only (cannot edit).

🔹 Admin Dashboard Features

✅ Dashboard Overview

Total users (agents).

Total votes cast.

Total wards.

Total LGAs.

Latest votes submission.

Recent users.

✅ User Management

Create, edit, delete users.

Assign ward & local government.

✅ Ward Management

Create, edit, delete wards.

✅ LGA Management

Create, edit, delete LGAs.

✅ Party Management

Create party with name & logo.

✅ Vote Submission Management

View votes analysis per party.

Add votes on behalf of agents.

See all votes (filter by ward/LGA).

Export votes to PDF or Excel.

View detailed result submission (with image of sheet, agent info, submission time).

Print result pages.

✅ Reported Issues Management

See all reported issues from users.

See proof (images/videos).

Respond or flag them.

🔹 Database Design (MySQL)

Users Table

id

name

phone

ward_id

lga_id

password (hashed)

role (agent/admin)

Wards Table

id

name

lga_id

LGAs Table

id

name

Parties Table

id

name

logo

Votes Table

id

user_id

ward_id

lga_id

party_id

votes (int)

result_sheet (image path)

edit_count (max 3)

created_at / updated_at

Issues Table

id

user_id

issue_type (from admin)

message (text)

image (nullable)

video (nullable)

created_at

🔹 Flow

Agent logs in → lands on dashboard

Submits results → uploads sheet → stored in DB → admin sees it instantly.

Reports issue → admin sees in real-time.

Admin monitors analytics → exports results → generates reports.

🔹 Implementation Plan

Backend (Laravel API)

Authentication (phone/ward ID login).

Endpoints for results, issues, contact, wards, LGAs, parties.

Role-based access control.

Frontend (Web with Bootstrap)

Admin dashboard + web version for agents.

Bootstrap for fast UI + charts for analytics.

Mobile App (React Native)

Agent dashboard with same features.

API calls to Laravel backend.